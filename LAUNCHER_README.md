# Mobile-use 启动脚本使用指南

这些脚本帮助你轻松管理和运行 Mobile-use 自动化任务。

## 📁 脚本文件

- **`mobile-use-launcher.sh`** - 主启动器，提供图形化菜单界面
- **`start-maestro.sh`** - Maestro Studio 服务管理脚本
- **`run-mobile-use.sh`** - Mobile-use 任务执行脚本

## 🚀 快速开始

### 1. 一键启动（推荐）

```bash
./mobile-use-launcher.sh
```

这将打开一个交互式菜单，包含所有功能。

### 2. 直接使用单独脚本

#### 启动 Maestro Studio 服务

```bash
# 启动服务（自动检测设备）
./start-maestro.sh start

# 启动并指定设备
./start-maestro.sh start 320646214105 android

# 查看服务状态
./start-maestro.sh status

# 停止服务
./start-maestro.sh stop
```

#### 运行 Mobile-use 任务

```bash
# 运行简单任务
./run-mobile-use.sh run "Take a screenshot of the current screen" screenshot-test

# 交互式创建任务
./run-mobile-use.sh interactive

# 查看任务示例
./run-mobile-use.sh examples

# 环境检查
./run-mobile-use.sh check
```

## 📋 功能特性

### 🔧 服务管理
- ✅ 自动启动/停止 Maestro Studio
- ✅ 服务状态检查
- ✅ 设备连接检测
- ✅ 端口冲突处理

### 🎯 任务执行
- ✅ 快速任务模板
- ✅ 交互式任务创建
- ✅ 自动环境检查
- ✅ 任务追踪记录

### 📊 系统管理
- ✅ 依赖检查
- ✅ 日志查看
- ✅ 临时文件清理
- ✅ 错误诊断

## 🛠️ 环境要求

### 必需依赖
- **uv** - Python 包管理器
- **Maestro** - 移动设备自动化工具
- **ADB** - Android 调试桥（Android 设备）

### 环境变量
在 `.env` 文件中配置以下变量：

```bash
# LLM API 配置（至少配置一个）
AZURE_OPENAI_API_KEY=your_azure_key
AZURE_OPENAI_ENDPOINT=your_azure_endpoint
AZURE_OPENAI_API_VERSION=2024-10-21

# 或者使用 OpenAI
OPENAI_API_KEY=your_openai_key

# 或者使用 Google
GOOGLE_API_KEY=your_google_key

# 可选：输出文件路径
EVENTS_OUTPUT_PATH=./events.json
RESULTS_OUTPUT_PATH=./results.json
```

## 📱 设备准备

### Android 设备
1. 启用开发者选项
2. 开启 USB 调试
3. 连接设备并授权调试
4. 验证连接：`adb devices`

### iOS 设备（需要 macOS）
1. 安装 Xcode Command Line Tools
2. 启动 iOS 模拟器
3. 验证连接：`xcrun simctl list devices`

## 🎮 使用示例

### 示例 1：截图测试
```bash
./mobile-use-launcher.sh
# 选择 "5) 运行快速任务"
# 选择 "1) 截图测试"
```

### 示例 2：自定义任务
```bash
./run-mobile-use.sh run "Open WhatsApp and send a message to John" whatsapp-test
```

### 示例 3：带输出描述的任务
```bash
./run-mobile-use.sh run "Check the weather app" weather-test traces "JSON with temperature and location"
```

## 📂 输出文件

### 任务追踪
- 位置：`traces/` 目录
- 格式：JSON 文件
- 内容：完整的任务执行记录

### 日志文件
- `maestro.log` - Maestro Studio 服务日志
- 环境变量指定的输出文件

## 🔍 故障排除

### 常见问题

1. **Maestro Studio 启动失败**
   ```bash
   ./start-maestro.sh status
   cat maestro.log
   ```

2. **设备连接问题**
   ```bash
   adb devices
   adb kill-server && adb start-server
   ```

3. **依赖缺失**
   ```bash
   ./run-mobile-use.sh check
   ```

4. **权限问题**
   ```bash
   chmod +x *.sh
   ```

### 重置环境
```bash
# 停止所有服务
./start-maestro.sh stop

# 清理临时文件
rm -f maestro.log
rm -rf traces/*

# 重新同步依赖
uv sync

# 重新启动
./mobile-use-launcher.sh
```

## 📞 获取帮助

- 运行 `./mobile-use-launcher.sh` 并选择 "11) 帮助文档"
- 查看各脚本的 `--help` 选项
- 检查日志文件了解详细错误信息

## 🎉 快速测试

运行以下命令验证环境：

```bash
# 1. 启动主界面
./mobile-use-launcher.sh

# 2. 选择 "8) 环境检查"

# 3. 选择 "1) 启动 Maestro Studio"

# 4. 选择 "5) 运行快速任务" -> "1) 截图测试"
```

如果所有步骤都成功，说明环境配置正确！
