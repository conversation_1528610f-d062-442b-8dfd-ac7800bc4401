#!/bin/bash

# Mobile-use 一键启动器
# 整合 Maestro 服务管理和任务执行

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

log_success() {
    echo -e "${GREEN}✓${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

log_error() {
    echo -e "${RED}❌${NC} $1"
}

log_header() {
    echo -e "${PURPLE}=== $1 ===${NC}"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    Mobile-use Launcher                      ║"
    echo "║              AI-powered Mobile Device Automation            ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 显示主菜单
show_menu() {
    echo ""
    echo "请选择操作:"
    echo ""
    echo "  ${GREEN}服务管理:${NC}"
    echo "    1) 启动 Maestro Studio"
    echo "    2) 停止 Maestro Studio"
    echo "    3) 重启 Maestro Studio"
    echo "    4) 查看服务状态"
    echo ""
    echo "  ${BLUE}任务执行:${NC}"
    echo "    5) 运行快速任务"
    echo "    6) 交互式创建任务"
    echo "    7) 查看任务示例"
    echo ""
    echo "  ${YELLOW}系统管理:${NC}"
    echo "    8) 环境检查"
    echo "    9) 查看日志"
    echo "   10) 清理临时文件"
    echo ""
    echo "  ${PURPLE}其他:${NC}"
    echo "   11) 帮助文档"
    echo "    0) 退出"
    echo ""
}

# 快速任务菜单
show_quick_tasks() {
    echo ""
    echo "快速任务:"
    echo ""
    echo "  1) 截图测试"
    echo "  2) 打开设置应用"
    echo "  3) 打开计算器"
    echo "  4) 打开浏览器"
    echo "  5) 检查通知"
    echo "  6) 自定义任务"
    echo "  0) 返回主菜单"
    echo ""
}

# 执行快速任务
run_quick_task() {
    local choice=$1
    
    case $choice in
        1)
            log_info "执行截图测试..."
            ./run-mobile-use.sh run "Take a screenshot of the current screen" "screenshot-$(date +%Y%m%d_%H%M%S)"
            ;;
        2)
            log_info "打开设置应用..."
            ./run-mobile-use.sh run "Open the Settings app and navigate to the main settings page" "open-settings-$(date +%Y%m%d_%H%M%S)"
            ;;
        3)
            log_info "打开计算器..."
            ./run-mobile-use.sh run "Open the calculator app" "open-calculator-$(date +%Y%m%d_%H%M%S)"
            ;;
        4)
            log_info "打开浏览器..."
            ./run-mobile-use.sh run "Open the default browser app" "open-browser-$(date +%Y%m%d_%H%M%S)"
            ;;
        5)
            log_info "检查通知..."
            ./run-mobile-use.sh run "Pull down the notification panel and check for notifications" "check-notifications-$(date +%Y%m%d_%H%M%S)"
            ;;
        6)
            read -p "请输入自定义任务目标: " custom_goal
            if [ -n "$custom_goal" ]; then
                local task_name="custom-task-$(date +%Y%m%d_%H%M%S)"
                log_info "执行自定义任务: $custom_goal"
                ./run-mobile-use.sh run "$custom_goal" "$task_name"
            else
                log_error "任务目标不能为空"
            fi
            ;;
        0)
            return 0
            ;;
        *)
            log_error "无效选择"
            ;;
    esac
}

# 查看日志
view_logs() {
    echo ""
    echo "可用日志文件:"
    echo ""
    
    local log_files=()
    
    # Maestro 日志
    if [ -f "maestro.log" ]; then
        log_files+=("maestro.log")
        echo "  1) Maestro Studio 日志 (maestro.log)"
    fi
    
    # 追踪文件
    if [ -d "traces" ]; then
        local trace_count=$(find traces -name "*.json" 2>/dev/null | wc -l)
        if [ $trace_count -gt 0 ]; then
            echo "  2) 任务追踪文件 (traces/ 目录)"
        fi
    fi
    
    # 环境输出文件
    if [ -n "$EVENTS_OUTPUT_PATH" ] && [ -f "$EVENTS_OUTPUT_PATH" ]; then
        echo "  3) 事件输出 ($EVENTS_OUTPUT_PATH)"
    fi
    
    if [ -n "$RESULTS_OUTPUT_PATH" ] && [ -f "$RESULTS_OUTPUT_PATH" ]; then
        echo "  4) 结果输出 ($RESULTS_OUTPUT_PATH)"
    fi
    
    echo "  0) 返回主菜单"
    echo ""
    
    read -p "请选择要查看的日志 (0-4): " log_choice
    
    case $log_choice in
        1)
            if [ -f "maestro.log" ]; then
                log_info "显示 Maestro Studio 日志 (最后 50 行):"
                tail -50 maestro.log
            else
                log_warning "Maestro 日志文件不存在"
            fi
            ;;
        2)
            if [ -d "traces" ]; then
                log_info "追踪文件列表:"
                ls -la traces/
            else
                log_warning "追踪目录不存在"
            fi
            ;;
        3)
            if [ -n "$EVENTS_OUTPUT_PATH" ] && [ -f "$EVENTS_OUTPUT_PATH" ]; then
                log_info "显示事件输出:"
                cat "$EVENTS_OUTPUT_PATH"
            else
                log_warning "事件输出文件不存在"
            fi
            ;;
        4)
            if [ -n "$RESULTS_OUTPUT_PATH" ] && [ -f "$RESULTS_OUTPUT_PATH" ]; then
                log_info "显示结果输出:"
                cat "$RESULTS_OUTPUT_PATH"
            else
                log_warning "结果输出文件不存在"
            fi
            ;;
        0)
            return 0
            ;;
        *)
            log_error "无效选择"
            ;;
    esac
    
    echo ""
    read -p "按 Enter 键继续..."
}

# 清理临时文件
cleanup() {
    log_info "清理临时文件..."
    
    # 清理日志文件
    if [ -f "maestro.log" ]; then
        read -p "删除 Maestro 日志文件? (y/N): " confirm
        if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
            rm -f maestro.log
            log_success "已删除 maestro.log"
        fi
    fi
    
    # 清理旧的追踪文件
    if [ -d "traces" ]; then
        local trace_count=$(find traces -name "*.json" -mtime ****>/dev/null | wc -l)
        if [ $trace_count -gt 0 ]; then
            read -p "删除 7 天前的追踪文件? (y/N): " confirm
            if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
                find traces -name "*.json" -mtime +7 -delete
                log_success "已删除旧的追踪文件"
            fi
        fi
    fi
    
    log_success "清理完成"
}

# 显示帮助
show_help() {
    echo ""
    echo "Mobile-use 使用指南:"
    echo ""
    echo "1. 首次使用:"
    echo "   - 确保已连接 Android 设备或启动模拟器"
    echo "   - 运行环境检查确保所有依赖已安装"
    echo "   - 启动 Maestro Studio 服务"
    echo ""
    echo "2. 运行任务:"
    echo "   - 使用快速任务进行常见操作"
    echo "   - 使用交互式创建器自定义任务"
    echo "   - 查看任务示例获取灵感"
    echo ""
    echo "3. 故障排除:"
    echo "   - 检查服务状态确保 Maestro Studio 正在运行"
    echo "   - 查看日志文件了解错误信息"
    echo "   - 重启服务解决连接问题"
    echo ""
    echo "4. 文件位置:"
    echo "   - 任务追踪: traces/ 目录"
    echo "   - Maestro 日志: maestro.log"
    echo "   - 配置文件: .env"
    echo ""
    echo "5. 环境变量:"
    echo "   - AZURE_OPENAI_API_KEY: Azure OpenAI API 密钥"
    echo "   - OPENAI_API_KEY: OpenAI API 密钥"
    echo "   - GOOGLE_API_KEY: Google API 密钥"
    echo "   - EVENTS_OUTPUT_PATH: 事件输出文件路径"
    echo "   - RESULTS_OUTPUT_PATH: 结果输出文件路径"
    echo ""
}

# 主循环
main_loop() {
    while true; do
        show_menu
        read -p "请选择 (0-11): " choice
        
        case $choice in
            1)
                log_header "启动 Maestro Studio"
                ./start-maestro.sh start
                ;;
            2)
                log_header "停止 Maestro Studio"
                ./start-maestro.sh stop
                ;;
            3)
                log_header "重启 Maestro Studio"
                ./start-maestro.sh restart
                ;;
            4)
                log_header "服务状态"
                ./start-maestro.sh status
                ;;
            5)
                log_header "快速任务"
                while true; do
                    show_quick_tasks
                    read -p "请选择任务 (0-6): " task_choice
                    if [ "$task_choice" = "0" ]; then
                        break
                    else
                        run_quick_task "$task_choice"
                        echo ""
                        read -p "按 Enter 键继续..."
                    fi
                done
                ;;
            6)
                log_header "交互式任务创建"
                ./run-mobile-use.sh interactive
                ;;
            7)
                log_header "任务示例"
                ./run-mobile-use.sh examples
                ;;
            8)
                log_header "环境检查"
                ./run-mobile-use.sh check
                ;;
            9)
                log_header "查看日志"
                view_logs
                ;;
            10)
                log_header "清理临时文件"
                cleanup
                ;;
            11)
                log_header "帮助文档"
                show_help
                ;;
            0)
                log_info "退出程序"
                exit 0
                ;;
            *)
                log_error "无效选择，请重新输入"
                ;;
        esac
        
        echo ""
        read -p "按 Enter 键继续..."
    done
}

# 主函数
main() {
    show_banner
    
    # 检查脚本文件是否存在
    if [ ! -f "start-maestro.sh" ] || [ ! -f "run-mobile-use.sh" ]; then
        log_error "缺少必要的脚本文件"
        log_info "请确保以下文件存在:"
        echo "  - start-maestro.sh"
        echo "  - run-mobile-use.sh"
        exit 1
    fi
    
    # 设置执行权限
    chmod +x start-maestro.sh run-mobile-use.sh
    
    # 进入主循环
    main_loop
}

# 执行主函数
main "$@"
