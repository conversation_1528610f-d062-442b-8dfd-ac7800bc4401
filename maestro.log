
╭────────────────────────────────────────────────────────────────────────────────╮
│                                                                                │
│          Download the new and improved Maestro Studio app today!               │
│                                                                                │
│ https://maestro.dev?utm_source=cli&utm_campaign=download_studio#maestro-studio │
│                                                                                │
╰────────────────────────────────────────────────────────────────────────────────╯


╭────────────────────────────────────────────────────────╮
│                                                        │
│   Maestro Studio is running at http://localhost:9999   │
│                                                        │
╰────────────────────────────────────────────────────────╯


Tip: Maestro Studio can now run simultaneously alongside other Maestro CLI commands!

Navigate to http://localhost:9999 in your browser to open Maestro Studio. Ctrl-C to exit.
Exception in thread "pool-5-thread-1" java.io.IOException: Command failed (host:transport:320646214105): device '320646214105' not found
	at dadb.adbserver.AdbServer.send$dadb(AdbServer.kt:103)
	at dadb.adbserver.AdbServerDadb.open(AdbServer.kt:148)
	at dadb.forwarding.TcpForwarder.handleForwarding$lambda$1(TcpForwarder.kt:64)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
