
╭────────────────────────────────────────────────────────────────────────────────╮
│                                                                                │
│          Download the new and improved Maestro Studio app today!               │
│                                                                                │
│ https://maestro.dev?utm_source=cli&utm_campaign=download_studio#maestro-studio │
│                                                                                │
╰────────────────────────────────────────────────────────────────────────────────╯

Running on 4f7d025f

Failed to install apk /var/folders/nv/57mj050d31z9c5w2q4wwtfy80000gn/T/maestro-app5114338192458126149.apk: Install failed: Failure [INSTALL_FAILED_USER_RESTRICTED: Install canceled by user]


The stack trace was:
java.io.IOException: Failed to install apk /var/folders/nv/57mj050d31z9c5w2q4wwtfy80000gn/T/maestro-app5114338192458126149.apk: Install failed: Failure [INSTALL_FAILED_USER_RESTRICTED: Install canceled by user]

	at maestro.drivers.AndroidDriver.install(AndroidDriver.kt:1194)
	at maestro.drivers.AndroidDriver.installMaestroDriverApp$lambda$78(AndroidDriver.kt:1118)
	at maestro.utils.Metrics.measured(Metrics.kt:48)
	at maestro.drivers.AndroidDriver.installMaestroDriverApp(AndroidDriver.kt:1107)
	at maestro.drivers.AndroidDriver.installMaestroApks(AndroidDriver.kt:1145)
	at maestro.drivers.AndroidDriver.open(AndroidDriver.kt:101)
	at maestro.Maestro$Companion.android(Maestro.kt:627)
	at maestro.cli.session.MaestroSessionManager.createAndroid(MaestroSessionManager.kt:332)
	at maestro.cli.session.MaestroSessionManager.createMaestro(MaestroSessionManager.kt:203)
	at maestro.cli.session.MaestroSessionManager.newSession(MaestroSessionManager.kt:104)
	at maestro.cli.session.MaestroSessionManager.newSession$default(MaestroSessionManager.kt:65)
	at maestro.cli.command.StudioCommand.call(StudioCommand.kt:75)
	at maestro.cli.command.StudioCommand.call(StudioCommand.kt:20)
	at picocli.CommandLine.executeUserObject(CommandLine.java:1953)
	at picocli.CommandLine.access$1300(CommandLine.java:145)
	at picocli.CommandLine$RunLast.executeUserObjectOfLastSubcommandWithSameParent(CommandLine.java:2358)
	at picocli.CommandLine$RunLast.handle(CommandLine.java:2352)
	at picocli.CommandLine$RunLast.handle(CommandLine.java:2314)
	at picocli.CommandLine$AbstractParseResultHandler.execute(CommandLine.java:2179)
	at picocli.CommandLine$RunLast.execute(CommandLine.java:2316)
	at maestro.cli.DisableAnsiMixin$Companion.executionStrategy(DisableAnsiMixin.kt:22)
	at picocli.CommandLine.execute(CommandLine.java:2078)
	at maestro.cli.AppKt.main(App.kt:158)
Caused by: java.io.IOException: Install failed: Failure [INSTALL_FAILED_USER_RESTRICTED: Install canceled by user]

	at dadb.Dadb$DefaultImpls.install(Dadb.kt:93)
	at dadb.adbserver.AdbServerDadb.install(AdbServer.kt:122)
	at dadb.Dadb$DefaultImpls.install(Dadb.kt:79)
	at dadb.adbserver.AdbServerDadb.install(AdbServer.kt:122)
	at maestro.drivers.AndroidDriver.install(AndroidDriver.kt:1192)
	... 22 more

