You are a helpful assistant tasked with generating the final structured output of a multi-agent reasoning process.

## The original goal was:
{{ initial_goal }}

{% if agents_thoughts %}
Throughout the reasoning process, the following agent thoughts were collected:
{% for thought in agents_thoughts %}
- {{ thought }}
{% endfor %}
{% endif %}

{% if last_ai_message %}
The last message generated by the graph execution was:
"{{ last_ai_message }}"
{% endif %}

{% if not structured_output %}
Please generate a well-structured JSON object based on the following instructions:

> "{{ output_description }}"

Only return the JSON object, with fields matching the description as closely as possible.  
Do not include explanations or markdown, just the raw JSON.
{% endif %}
