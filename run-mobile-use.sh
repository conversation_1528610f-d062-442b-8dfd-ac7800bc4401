#!/bin/bash

# Mobile-use 任务运行脚本
# 用于运行 mobile-use 自动化任务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

log_success() {
    echo -e "${GREEN}✓${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

log_error() {
    echo -e "${RED}❌${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查 uv
    if ! command -v uv &> /dev/null; then
        log_error "uv 未安装"
        log_info "请运行以下命令安装 uv:"
        echo "curl -LsSf https://astral.sh/uv/install.sh | sh"
        exit 1
    fi
    log_success "uv 已安装: $(uv --version)"
    
    # 检查 Maestro
    if ! command -v maestro &> /dev/null; then
        log_error "Maestro 未安装"
        log_info "请运行以下命令安装 Maestro:"
        echo "curl -Ls \"https://get.maestro.mobile.dev\" | bash"
        exit 1
    fi
    log_success "Maestro 已安装: $(maestro --version)"
    
    # 检查虚拟环境
    if [ ! -d ".venv" ]; then
        log_warning "虚拟环境不存在，正在创建..."
        uv sync
        log_success "虚拟环境已创建"
    else
        log_success "虚拟环境已存在"
    fi
}

# 检查 Maestro Studio 状态
check_maestro_studio() {
    log_info "检查 Maestro Studio 状态..."
    
    if curl -s http://localhost:9999/api/banner-message >/dev/null 2>&1; then
        log_success "Maestro Studio 正在运行"
        return 0
    else
        log_warning "Maestro Studio 未运行"
        log_info "正在启动 Maestro Studio..."
        
        # 尝试启动 Maestro Studio
        ./start-maestro.sh start
        
        if [ $? -eq 0 ]; then
            log_success "Maestro Studio 启动成功"
            return 0
        else
            log_error "Maestro Studio 启动失败"
            return 1
        fi
    fi
}

# 检查环境配置
check_environment() {
    log_info "检查环境配置..."
    
    # 检查 .env 文件
    if [ ! -f ".env" ]; then
        log_error ".env 文件不存在"
        log_info "请创建 .env 文件并配置必要的环境变量"
        exit 1
    fi
    log_success ".env 文件存在"
    
    # 检查关键环境变量
    source .env
    
    if [ -z "$AZURE_OPENAI_API_KEY" ] && [ -z "$OPENAI_API_KEY" ] && [ -z "$GOOGLE_API_KEY" ]; then
        log_error "未配置 LLM API 密钥"
        log_info "请在 .env 文件中配置以下之一:"
        echo "  - AZURE_OPENAI_API_KEY"
        echo "  - OPENAI_API_KEY"
        echo "  - GOOGLE_API_KEY"
        exit 1
    fi
    log_success "LLM API 密钥已配置"
}

# 运行 mobile-use 任务
run_task() {
    local goal="$1"
    local test_name="$2"
    local traces_path="${3:-traces}"
    local output_description="$4"
    
    if [ -z "$goal" ]; then
        log_error "未指定任务目标"
        show_help
        exit 1
    fi
    
    log_info "准备运行任务: $goal"
    
    # 构建命令
    local cmd="uv run mobile-use \"$goal\""
    
    if [ -n "$test_name" ]; then
        cmd="$cmd --test-name \"$test_name\""
    fi
    
    if [ "$traces_path" != "traces" ]; then
        cmd="$cmd --traces-path \"$traces_path\""
    fi
    
    if [ -n "$output_description" ]; then
        cmd="$cmd --output-description \"$output_description\""
    fi
    
    log_info "执行命令: $cmd"
    log_info "开始执行任务..."
    
    # 设置 PATH 以包含 Maestro
    export PATH="$PATH:$HOME/.maestro/bin"
    
    # 执行任务
    eval $cmd
    
    if [ $? -eq 0 ]; then
        log_success "任务执行完成"
        
        # 显示输出文件位置
        if [ -n "$test_name" ]; then
            log_info "追踪文件保存在: $traces_path/$test_name"
        fi
        
        if [ -n "$EVENTS_OUTPUT_PATH" ]; then
            log_info "事件输出保存在: $EVENTS_OUTPUT_PATH"
        fi
        
        if [ -n "$RESULTS_OUTPUT_PATH" ]; then
            log_info "结果输出保存在: $RESULTS_OUTPUT_PATH"
        fi
    else
        log_error "任务执行失败"
        exit 1
    fi
}

# 显示预定义任务
show_predefined_tasks() {
    echo "预定义任务示例:"
    echo ""
    echo "1. 截图任务:"
    echo "   $0 run \"Take a screenshot of the current screen\" screenshot-test"
    echo ""
    echo "2. 应用启动:"
    echo "   $0 run \"Open the Settings app\" open-settings"
    echo ""
    echo "3. 文本输入:"
    echo "   $0 run \"Open the calculator app and calculate 123 + 456\" calculator-test"
    echo ""
    echo "4. 网页浏览:"
    echo "   $0 run \"Open Chrome and navigate to google.com\" browse-google"
    echo ""
    echo "5. 应用交互:"
    echo "   $0 run \"Open WhatsApp and check for new messages\" whatsapp-check"
}

# 交互式任务创建
interactive_task() {
    echo "=== 交互式任务创建 ==="
    echo ""
    
    # 获取任务目标
    read -p "请输入任务目标: " goal
    if [ -z "$goal" ]; then
        log_error "任务目标不能为空"
        exit 1
    fi
    
    # 获取测试名称
    read -p "请输入测试名称 (可选): " test_name
    
    # 获取输出描述
    read -p "请输入输出描述 (可选): " output_description
    
    # 确认执行
    echo ""
    echo "任务配置:"
    echo "  目标: $goal"
    echo "  测试名称: ${test_name:-无}"
    echo "  输出描述: ${output_description:-无}"
    echo ""
    
    read -p "确认执行? (y/N): " confirm
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        log_info "任务已取消"
        exit 0
    fi
    
    # 执行任务
    run_task "$goal" "$test_name" "traces" "$output_description"
}

# 显示帮助信息
show_help() {
    echo "Mobile-use 任务运行脚本"
    echo ""
    echo "用法:"
    echo "  $0 run <goal> [test_name] [traces_path] [output_description]"
    echo "  $0 interactive                    - 交互式创建任务"
    echo "  $0 examples                       - 显示预定义任务示例"
    echo "  $0 check                          - 检查环境和依赖"
    echo "  $0 help                           - 显示帮助信息"
    echo ""
    echo "参数:"
    echo "  goal               - 任务目标 (必需)"
    echo "  test_name          - 测试名称 (可选)"
    echo "  traces_path        - 追踪文件保存路径 (默认: traces)"
    echo "  output_description - 输出描述 (可选)"
    echo ""
    echo "示例:"
    echo "  $0 run \"Take a screenshot\" screenshot-test"
    echo "  $0 run \"Open Settings app\" settings-test traces \"JSON with app info\""
    echo "  $0 interactive"
}

# 环境检查
check_all() {
    log_info "=== 环境检查 ==="
    check_dependencies
    check_environment
    check_maestro_studio
    log_success "所有检查通过，环境准备就绪！"
}

# 主函数
main() {
    local action=${1:-help}
    
    case $action in
        run)
            check_dependencies
            check_environment
            check_maestro_studio
            run_task "$2" "$3" "$4" "$5"
            ;;
        interactive)
            check_dependencies
            check_environment
            check_maestro_studio
            interactive_task
            ;;
        examples)
            show_predefined_tasks
            ;;
        check)
            check_all
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $action"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
