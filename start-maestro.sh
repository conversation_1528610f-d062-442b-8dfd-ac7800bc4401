#!/bin/bash

# Mobile-use Maestro 启动脚本
# 用于启动 Maestro Studio 服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

log_success() {
    echo -e "${GREEN}✓${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

log_error() {
    echo -e "${RED}❌${NC} $1"
}

# 检查 Maestro 是否已安装
check_maestro() {
    if ! command -v maestro &> /dev/null; then
        log_error "Maestro 未安装"
        log_info "请运行以下命令安装 Maestro:"
        echo "curl -Ls \"https://get.maestro.mobile.dev\" | bash"
        exit 1
    fi
    log_success "Maestro 已安装: $(maestro --version)"
}

# 检查设备连接
check_devices() {
    log_info "检查设备连接状态..."
    
    # 检查 Android 设备
    if command -v adb &> /dev/null; then
        android_devices=$(adb devices | grep -v "List of devices" | grep "device$" | wc -l)
        if [ "$android_devices" -gt 0 ]; then
            log_success "发现 $android_devices 个 Android 设备"
            adb devices | grep "device$" | while read line; do
                device_id=$(echo $line | cut -d$'\t' -f1)
                log_info "  - $device_id"
            done
        else
            log_warning "未发现 Android 设备"
        fi
    else
        log_warning "ADB 未安装，无法检查 Android 设备"
    fi
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # 端口被占用
    else
        return 1  # 端口空闲
    fi
}

# 启动 Maestro Studio
start_maestro() {
    local device_id=$1
    local platform=${2:-android}
    
    log_info "启动 Maestro Studio..."
    
    # 检查端口 9999 是否被占用
    if check_port 9999; then
        log_warning "端口 9999 已被占用，可能 Maestro Studio 已在运行"
        
        # 测试健康检查
        if curl -s http://localhost:9999/api/banner-message >/dev/null 2>&1; then
            log_success "Maestro Studio 已在运行并响应正常"
            return 0
        else
            log_error "端口 9999 被占用但 Maestro Studio 未响应"
            log_info "请手动检查并停止占用端口的进程"
            exit 1
        fi
    fi
    
    # 构建 Maestro 命令
    local cmd="maestro --platform $platform"
    if [ -n "$device_id" ]; then
        cmd="$cmd --device $device_id"
    fi
    cmd="$cmd studio --no-window"
    
    log_info "执行命令: $cmd"
    
    # 启动 Maestro Studio（后台运行）
    nohup $cmd > maestro.log 2>&1 &
    local maestro_pid=$!
    
    log_info "Maestro Studio 已启动 (PID: $maestro_pid)"
    log_info "等待服务启动..."
    
    # 等待服务启动（最多等待 30 秒）
    local max_wait=30
    local wait_time=0
    while [ $wait_time -lt $max_wait ]; do
        if curl -s http://localhost:9999/api/banner-message >/dev/null 2>&1; then
            log_success "Maestro Studio 启动成功！"
            log_success "服务地址: http://localhost:9999"
            return 0
        fi
        sleep 2
        wait_time=$((wait_time + 2))
        echo -n "."
    done
    
    echo ""
    log_error "Maestro Studio 启动超时"
    log_info "请检查日志文件: maestro.log"
    exit 1
}

# 停止 Maestro Studio
stop_maestro() {
    log_info "停止 Maestro Studio..."
    
    # 查找 Maestro 进程
    local pids=$(pgrep -f "maestro.*studio" || true)
    
    if [ -z "$pids" ]; then
        log_warning "未找到运行中的 Maestro Studio 进程"
        return 0
    fi
    
    # 停止进程
    echo "$pids" | while read pid; do
        if [ -n "$pid" ]; then
            log_info "停止进程 $pid"
            kill "$pid" 2>/dev/null || true
        fi
    done
    
    # 等待进程停止
    sleep 3
    
    # 检查是否还有进程运行
    local remaining_pids=$(pgrep -f "maestro.*studio" || true)
    if [ -n "$remaining_pids" ]; then
        log_warning "强制停止剩余进程"
        echo "$remaining_pids" | while read pid; do
            if [ -n "$pid" ]; then
                kill -9 "$pid" 2>/dev/null || true
            fi
        done
    fi
    
    log_success "Maestro Studio 已停止"
}

# 显示状态
show_status() {
    log_info "Maestro Studio 状态检查"
    
    if check_port 9999; then
        if curl -s http://localhost:9999/api/banner-message >/dev/null 2>&1; then
            log_success "Maestro Studio 正在运行 (http://localhost:9999)"
        else
            log_warning "端口 9999 被占用但服务未响应"
        fi
    else
        log_warning "Maestro Studio 未运行"
    fi
}

# 显示帮助信息
show_help() {
    echo "Mobile-use Maestro 启动脚本"
    echo ""
    echo "用法:"
    echo "  $0 start [device_id] [platform]  - 启动 Maestro Studio"
    echo "  $0 stop                          - 停止 Maestro Studio"
    echo "  $0 status                        - 显示服务状态"
    echo "  $0 restart [device_id] [platform] - 重启 Maestro Studio"
    echo "  $0 help                          - 显示帮助信息"
    echo ""
    echo "参数:"
    echo "  device_id  - 设备ID (可选，如果不指定会自动选择)"
    echo "  platform   - 平台 (android/ios，默认: android)"
    echo ""
    echo "示例:"
    echo "  $0 start                         - 启动 Maestro Studio (自动选择设备)"
    echo "  $0 start 320646214105 android    - 启动并连接指定 Android 设备"
    echo "  $0 stop                          - 停止 Maestro Studio"
}

# 主函数
main() {
    local action=${1:-help}
    
    case $action in
        start)
            check_maestro
            check_devices
            start_maestro "$2" "$3"
            ;;
        stop)
            stop_maestro
            ;;
        restart)
            stop_maestro
            sleep 2
            check_maestro
            check_devices
            start_maestro "$2" "$3"
            ;;
        status)
            show_status
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $action"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
