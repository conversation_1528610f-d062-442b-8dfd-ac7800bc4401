### 🚀 What's new?

_Describe the purpose of your pull request. What problem does it solve? What feature does it add? Link to any relevant issues!_

### 🤔 Type of Change

_What kind of change is this? Mark with an `x`_

- [ ] **Bug fix** (non-breaking change that solves an issue)
- [ ] **New feature** (non-breaking change that adds functionality)
- [ ] **Breaking change** (fix or feature that would cause existing functionality to change)
- [ ] **Documentation** (update to the docs)

### ✅ Checklist

_Before you submit, please make sure you've done the following. If you have any questions, we're here to help!_

- [ ] I have read the **[Contributing Guide](../CONTRIBUTING.md)**.
- [ ] My code follows the project's style guidelines (`ruff check .` and `ruff format .` pass).
- [ ] I have added necessary documentation (if applicable).

### 💬 Any questions or comments?

_Have a question or need some help? Join us on **[Discord](https://discord.gg/6nSqmQ9pQs)**!_
