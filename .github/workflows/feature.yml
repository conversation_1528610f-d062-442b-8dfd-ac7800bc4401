name: Feature Integration 🧩

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  ruff:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
      - name: Run ruff format check
        uses: astral-sh/ruff-action@v3
        with:
          args: "format --check"
      - name: Run ruff linter
        uses: astral-sh/ruff-action@v3
        with:
          args: "check"

  build:
    runs-on: ubuntu-latest
    steps:
      - name: Clone the repository
        uses: actions/checkout@v5

      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true

      - name: Build package
        run: uv build
