---
name: "🐞 Found a bug?"
about: "Found something that needs fixing? Let us know!"
title: "[BUG]"
labels: bug
assignees: ""
---

## 💥 The Bug

A clear and concise description of what the bug is.

## 📝 Steps to Reproduce

Steps to reproduce the behavior:

...

## 🤔 Expected Behavior

A clear and concise description of what you expected to happen.

## 📸 Screenshots / Logs

If applicable, add screenshots or log files to help explain your problem.
_For long logs, use a service like [Gist](https://gist.github.com) and paste the link here!_

---

## 💻 Your Environment

- **OS (host):** `uname -a` (Linux/macOS) or `cmd /c ver` (Windows)
- **Mobile OS:** [e.g. Android 13, iOS 16]
- **Device:** [e.g. Pixel 7, iPhone 14]
- **Device type:** [e.g. Physical device, Emulator]
- **SDK Version: (if installed via pip)** (`pip show mobile-use`)
- **Git Commit SHA: (if ran manually)** (`git rev-parse --short HEAD`)
- **How you ran it:** [e.g., <PERSON><PERSON>, <PERSON><PERSON><PERSON>]

## 🙏 Anything Else?

Add any other context about the problem here. Thanks for helping us improve!
