[project]
name = "minitap-mobile-use"
version = "2.1.0"
description = "AI-powered multi-agent system that automates real Android and iOS devices through low-level control using LangGraph."
readme = "README.md"
license = { file = "LICENSE" }

authors = [
    { name = "<PERSON><PERSON><PERSON>" },
    { name = "<PERSON><PERSON><PERSON>" },
    { name = "<PERSON>" },
]

requires-python = ">=3.12"

dependencies = [
    "langgraph>=0.6.6",
    "adbutils==2.9.3",
    "langchain-google-genai>=2.1.10",
    "langchain>=0.3.27",
    "langchain-core>=0.3.75",
    "jinja2==3.1.6",
    "python-dotenv==1.1.1",
    "pydantic-settings==2.10.1",
    "langchain-mcp-adapters==0.1.7",
    "langchain-openai==0.3.27",
    "typer==0.16.0",
    "langchain-cerebras>=0.5.0",
    "inquirer>=3.4.0",
    "sseclient-py==1.8.0",
    "fastapi==0.111.0",
    "uvicorn[standard]==0.30.1",
    "colorama>=0.4.6",
    "psutil>=5.9.0",
    "langchain-google-vertexai>=2.0.28",
]

[project.optional-dependencies]
dev = [
    "ruff==0.5.3",
    "pytest==8.4.1",
    "pytest-cov==5.0.0"
]

[project.scripts]
mobile-use = "minitap.mobile_use.main:cli"

[project.urls]
"Homepage" = "https://minitap.ai/"
"Source" = "https://github.com/minitap-ai/mobile-use"

[build-system]
requires = ["uv_build>=0.7.20,<0.8.0"]
build-backend = "uv_build"

[tool.uv.build-backend]
module-root = ""
module-name = "minitap.mobile_use"

[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    "venv",
]

# Same as Black.
line-length = 100
indent-width = 4

target-version = "py312"

[tool.ruff.lint]
# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`) codes by default.
# Also enable TID (flake8-tidy-imports) to enforce absolute imports
select = ["E", "F", "TID", "UP"]

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.lint.flake8-tidy-imports]
# Ban relative imports
ban-relative-imports = "all"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"
